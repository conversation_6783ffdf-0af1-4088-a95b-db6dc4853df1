# 测试PPT视角绑定功能指南

## 测试步骤

### 1. 启动应用并进入3D场景

1. 打开浏览器访问 `http://localhost:3101`
2. 登录系统（如果需要）
3. 进入3D场景页面

### 2. 切换到内景模式

1. 确保当前在内景模式下（PPT演示功能仅在内景模式下可用）
2. 如果在外景模式，请先切换到内景

### 3. 启动PPT演示模式

1. 点击底部导航栏的"PPT演示"按钮
2. PPT演示界面将在右侧打开
3. 左侧保持3D场景显示

### 4. 测试视角绑定功能

#### 4.1 打开视角绑定界面

1. 在PPT演示界面中找到"视角绑定"按钮
2. 点击打开视角绑定管理界面

#### 4.2 测试预设分类

1. 观察顶部的分类标签：
   - 📋 全部
   - 🏢 基础视角
   - 🌅 外景
   - 🏛️ 内景
   - 🖥️ 设备聚焦
   - ⭐ 特殊功能
   - 🎨 自定义

2. 点击不同的分类标签，观察预设列表的变化

#### 4.3 测试预设预览功能

1. 选择任意一个预设
2. 点击预设右侧的"预览"按钮
3. 观察3D场景是否切换到对应的视角
4. 检查是否应用了相应的功能特性（如透视、场景切换等）

#### 4.4 测试预设应用

1. 选择一个幻灯片（如第1页）
2. 点击"选择预设"按钮
3. 选择一个预设并点击应用
4. 确认预设已绑定到该幻灯片

#### 4.5 测试功能标签

观察预设卡片上的功能标签：
- **透视** - 紫色标签，表示会启用透视功能
- **巡检** - 绿色标签，表示会准备巡检模式
- **设备聚焦** - 橙色标签，表示会聚焦特定设备
- **内景/外景** - 蓝色标签，表示会切换场景类型

### 5. 测试特殊功能预设

#### 5.1 测试透视模式预设

1. 选择"👁️ 透视模式"预设
2. 点击预览或应用
3. 确认透视功能是否自动启用
4. 观察建筑结构是否变为透明

#### 5.2 测试场景切换预设

1. 选择外景预设（如"🌅 外景广角"）
2. 点击预览或应用
3. 确认是否自动切换到外景模式

#### 5.3 测试内景预设

1. 选择内景预设（如"🏛️ 内景主厅"）
2. 点击预览或应用
3. 确认是否自动切换到内景模式

### 6. 测试自定义预设创建

1. 在3D场景中手动调整到一个理想的视角
2. 在预设选择界面点击"保存当前视角为预设"
3. 输入预设名称和描述
4. 确认新预设出现在"自定义"分类中
5. 测试新预设的预览和应用功能

### 7. 测试数据持久化

1. 创建几个自定义预设
2. 刷新页面
3. 重新进入PPT演示模式
4. 确认自定义预设仍然存在

## 预期结果

### 功能正常的表现

1. **分类筛选** - 点击分类标签能正确筛选预设
2. **预设预览** - 点击预览按钮能立即切换视角
3. **功能应用** - 预设的特殊功能（透视、场景切换等）能正确应用
4. **视觉反馈** - 功能标签正确显示，UI响应流畅
5. **数据保存** - 自定义预设能正确保存和加载

### 可能的问题

1. **预设不生效** - 检查控制台是否有错误信息
2. **功能冲突** - 某些功能可能与当前状态冲突
3. **性能问题** - 频繁切换预设可能导致性能下降

## 调试信息

如果遇到问题，请检查浏览器控制台的以下信息：

1. **错误信息** - 查看是否有JavaScript错误
2. **网络请求** - 检查是否有API调用失败
3. **状态日志** - 查看GlobalThreeStore的状态变化

## 反馈收集

测试完成后，请记录：

1. **功能完整性** - 哪些功能正常工作，哪些有问题
2. **用户体验** - 界面是否直观，操作是否流畅
3. **性能表现** - 是否有卡顿或延迟
4. **建议改进** - 有什么可以优化的地方

## 总结

这个增强的PPT视角绑定功能为用户提供了：

- 12个默认预设，覆盖各种常用视角
- 6个分类，便于快速查找
- 预览功能，无需应用即可查看效果
- 功能标签，清晰显示预设能力
- 自定义预设，满足个性化需求
- 数据持久化，避免重复设置

通过这些功能，用户可以在PPT演示时更加丰富地展示3D场景，提升演示效果和观众体验。
