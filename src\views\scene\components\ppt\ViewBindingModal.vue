<template>
  <ModalDialog v-model:visible="visibleValue" title="PPT视角绑定管理" width="800px" height="600px" :show-footer="true" :iconSrc="dashboardTitle">
    <div class="h-full flex flex-col p-[1vw]">
      <!-- 工具栏 -->
      <div class="mb-[1vw] flex items-center justify-between">
        <div class="text-[0.9vw] text-white font-medium">当前幻灯片：{{ currentSlide + 1 }} / {{ totalSlides }}</div>
        <div class="flex gap-[0.8vw]">
          <button
            class="px-[1vw] py-[0.4vw] bg-blue-500/80 hover:bg-blue-500 text-white rounded text-[0.7vw] transition-all"
            @click="bindCurrentView"
          >
            绑定当前视角
          </button>
          <button
            class="px-[1vw] py-[0.4vw] bg-green-500/80 hover:bg-green-500 text-white rounded text-[0.7vw] transition-all"
            @click="previewBinding"
          >
            预览绑定
          </button>
        </div>
      </div>

      <!-- 幻灯片列表 -->
      <div class="flex-1 overflow-auto">
        <div class="grid grid-cols-2 gap-[1vw]">
          <div
            v-for="(slide, index) in slides"
            :key="index"
            class="relative bg-[#1a2332] border border-blue-400/30 rounded p-[0.8vw] cursor-pointer transition-all"
            :class="{ 'border-blue-400': currentSlide === index }"
            @click="selectSlide(index)"
          >
            <!-- 幻灯片预览 -->
            <div class="w-full h-[8vw] bg-[#0C1526] rounded mb-[0.5vw] flex items-center justify-center">
              <span class="text-[0.8vw] text-gray-400">幻灯片 {{ index + 1 }}</span>
            </div>

            <!-- 绑定信息 -->
            <div class="text-[0.7vw]">
              <div class="text-white mb-[0.2vw]">视角绑定：</div>
              <div v-if="getSlideBinding(index)" class="text-green-400">
                <div class="text-[0.8vw] text-white font-medium mb-[0.2vw]">{{ getSlideBinding(index)?.name }}</div>
                <!-- 场景类型和功能标签 -->
                <div class="flex gap-[0.3vw] mb-[0.3vw] flex-wrap">
                  <span v-if="getSlideBinding(index)?.sceneType" class="px-[0.4vw] py-[0.1vw] bg-blue-500/20 text-blue-300 rounded text-[0.5vw]">
                    {{ getSlideBinding(index)?.sceneType === 'exterior' ? '外景' : '内景' }}
                  </span>
                  <span
                    v-if="getSlideBinding(index)?.deviceFocus"
                    class="px-[0.4vw] py-[0.1vw] bg-orange-500/20 text-orange-300 rounded text-[0.5vw]"
                  >
                    设备: {{ getSlideBinding(index)?.deviceFocus }}
                  </span>
                  <span
                    v-if="getSlideBinding(index)?.features?.transparency"
                    class="px-[0.4vw] py-[0.1vw] bg-purple-500/20 text-purple-300 rounded text-[0.5vw]"
                  >
                    透视
                  </span>
                  <span
                    v-if="getSlideBinding(index)?.features?.patrol"
                    class="px-[0.4vw] py-[0.1vw] bg-green-500/20 text-green-300 rounded text-[0.5vw]"
                  >
                    巡检
                  </span>
                </div>
                <div class="text-[0.6vw] text-gray-400">
                  位置: ({{ getSlideBinding(index)?.cameraPosition.x.toFixed(1) }}, {{ getSlideBinding(index)?.cameraPosition.y.toFixed(1) }},
                  {{ getSlideBinding(index)?.cameraPosition.z.toFixed(1) }})
                </div>
              </div>
              <div v-else class="text-gray-500">未绑定</div>
            </div>

            <!-- 操作按钮 -->
            <div class="absolute top-[0.5vw] right-[0.5vw] flex gap-[0.3vw]">
              <button
                class="w-[1.5vw] h-[1.5vw] bg-blue-500/80 hover:bg-blue-500 text-white rounded text-[0.6vw] flex items-center justify-center transition-all"
                @click.stop="showPresetSelector(index)"
                title="选择预设视角"
              >
                +
              </button>
              <button
                v-if="getSlideBinding(index)"
                class="w-[1.5vw] h-[1.5vw] bg-red-500/80 hover:bg-red-500 text-white rounded text-[0.6vw] flex items-center justify-center transition-all"
                @click.stop="clearBinding(index)"
                title="清除绑定"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 预设视角选择模态框 -->
      <div v-if="showPresetModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]" @click="closePresetModal">
        <div class="bg-[#1a2332] border border-blue-400/30 rounded-lg p-[1.5vw] w-[40vw] max-h-[60vh] overflow-auto" @click.stop>
          <div class="flex items-center justify-between mb-[1vw]">
            <h3 class="text-[1vw] text-white font-medium">选择预设视角</h3>
            <button class="text-gray-400 hover:text-white text-[1.2vw]" @click="closePresetModal"> × </button>
          </div>

          <!-- 预设分类标签 -->
          <div class="flex flex-wrap gap-[0.5vw] mb-[1vw]">
            <button
              v-for="(category, key) in presetCategories"
              :key="key"
              class="px-[0.8vw] py-[0.3vw] rounded text-[0.6vw] transition-all"
              :class="selectedCategory === key ? 'bg-blue-500 text-white' : 'bg-gray-600/30 text-gray-300 hover:bg-gray-600/50'"
              @click="selectedCategory = key"
            >
              {{ category.icon }} {{ category.name }}
            </button>
          </div>

          <!-- 预设列表 -->
          <div class="space-y-[0.5vw] max-h-[35vh] overflow-auto">
            <div
              v-for="preset in filteredPresets"
              :key="preset.id"
              class="bg-[#0C1526] border border-gray-600/30 rounded p-[0.8vw] cursor-pointer hover:border-blue-400/50 transition-all"
              @click="selectPreset(preset)"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-[0.5vw]">
                    <span class="text-[1vw]">{{ preset.icon || '📷' }}</span>
                    <div class="text-[0.8vw] text-white font-medium">{{ preset.name }}</div>
                    <!-- 功能标签 -->
                    <div v-if="preset.features" class="flex gap-[0.3vw]">
                      <span v-if="preset.features.transparency" class="px-[0.4vw] py-[0.1vw] bg-purple-500/20 text-purple-300 rounded text-[0.5vw]"
                        >透视</span
                      >
                      <span v-if="preset.features.patrol" class="px-[0.4vw] py-[0.1vw] bg-green-500/20 text-green-300 rounded text-[0.5vw]"
                        >巡检</span
                      >
                      <span v-if="preset.features.deviceFocus" class="px-[0.4vw] py-[0.1vw] bg-orange-500/20 text-orange-300 rounded text-[0.5vw]"
                        >设备聚焦</span
                      >
                      <span v-if="preset.features.sceneType" class="px-[0.4vw] py-[0.1vw] bg-blue-500/20 text-blue-300 rounded text-[0.5vw]">{{
                        preset.features.sceneType === 'interior' ? '内景' : '外景'
                      }}</span>
                    </div>
                  </div>
                  <div v-if="preset.description" class="text-[0.6vw] text-gray-400 mt-[0.2vw]">{{ preset.description }}</div>
                  <div class="text-[0.6vw] text-gray-500 mt-[0.2vw]"> 创建时间: {{ formatDate(preset.createdAt) }} </div>
                </div>
                <div class="text-[0.6vw] text-gray-400 text-right">
                  <div class="mb-[0.2vw]"
                    >位置: ({{ preset.cameraPosition.x.toFixed(1) }}, {{ preset.cameraPosition.y.toFixed(1) }},
                    {{ preset.cameraPosition.z.toFixed(1) }})</div
                  >
                  <div
                    >目标: ({{ preset.cameraTarget.x.toFixed(1) }}, {{ preset.cameraTarget.y.toFixed(1) }},
                    {{ preset.cameraTarget.z.toFixed(1) }})</div
                  >
                  <!-- 预览按钮 -->
                  <button
                    class="mt-[0.3vw] px-[0.6vw] py-[0.2vw] bg-blue-500/20 hover:bg-blue-500/40 text-blue-300 rounded text-[0.5vw] transition-all"
                    @click.stop="previewPreset(preset)"
                  >
                    预览
                  </button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredPresets.length === 0" class="text-center py-[2vw] text-gray-400">
              <div class="text-[0.8vw]">该分类下暂无预设视角</div>
              <div class="text-[0.6vw] mt-[0.5vw]">请选择其他分类或创建新的预设</div>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div class="flex justify-between items-center mt-[1vw] pt-[1vw] border-t border-gray-600/30">
            <div class="flex gap-[0.5vw]">
              <button
                class="px-[1vw] py-[0.4vw] bg-green-500/80 hover:bg-green-500 text-white rounded text-[0.7vw] transition-all"
                @click="createNewPreset"
              >
                保存当前视角为预设
              </button>
              <button
                class="px-[1vw] py-[0.4vw] bg-orange-500/80 hover:bg-orange-500 text-white rounded text-[0.7vw] transition-all"
                @click="forceRecreatePresets"
                title="重新创建默认预设（包含功能特性）"
              >
                重新创建预设
              </button>
            </div>
            <button
              class="px-[1vw] py-[0.4vw] bg-gray-500/80 hover:bg-gray-500 text-white rounded text-[0.7vw] transition-all"
              @click="closePresetModal"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-[0.8vw]">
        <button
          class="px-[1vw] h-[1.8vw] flex items-center justify-center rounded bg-gray-500/80 hover:bg-gray-500 text-white transition-all text-[0.7vw]"
          @click="handleCancel"
        >
          取消
        </button>
        <button
          class="px-[1vw] h-[1.8vw] flex items-center justify-center rounded bg-blue-500/80 hover:bg-blue-500 text-white transition-all text-[0.7vw]"
          @click="handleConfirm"
        >
          确认
        </button>
      </div>
    </template>
  </ModalDialog>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import ModalDialog from '@/views/scene/components/ModalDialog.vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import { useGlobalThreeStore, type ViewPreset, type PresetType, type PPTViewBinding } from '@/views/scene/store/globalThreeStore';
  import { CameraController } from '@/views/scene/lib/CameraController';
  import { message } from 'ant-design-vue';

  interface ViewBinding {
    position: { x: number; y: number; z: number };
    target: { x: number; y: number; z: number };
  }

  interface Slide {
    id: number;
    name: string;
    viewBinding?: ViewBinding;
  }

  const props = defineProps<{
    visible: boolean;
    currentSlide?: number;
    totalSlides?: number;
  }>();

  const emit = defineEmits<{
    'update:visible': [value: boolean];
    confirm: [bindings: ViewBinding[]];
    'bind-current': [slideIndex: number];
    'preview-binding': [slideIndex: number];
  }>();

  const visibleValue = computed({
    get() {
      return props.visible;
    },
    set(value) {
      emit('update:visible', value);
    },
  });

  const globalThreeStore = useGlobalThreeStore();

  const currentSlide = ref(props.currentSlide || 0);
  const totalSlides = ref(props.totalSlides || 4);
  const slides = ref<Slide[]>([]);
  const showPresetModal = ref(false);
  const selectedSlideForPreset = ref<number>(-1);
  const selectedCategory = ref<PresetType | 'all'>('all');

  // 预设分类定义
  const presetCategories = computed(() => ({
    all: { name: '全部', icon: '📋' },
    basic: { name: '基础视角', icon: '🏢' },
    exterior: { name: '外景', icon: '🌅' },
    interior: { name: '内景', icon: '🏛️' },
    device: { name: '设备聚焦', icon: '🖥️' },
    special: { name: '特殊功能', icon: '⭐' },
    custom: { name: '自定义', icon: '🎨' },
  }));

  // 获取视角预设列表
  const viewPresets = computed(() => globalThreeStore.viewPresets);

  // 过滤后的预设列表
  const filteredPresets = computed(() => {
    if (selectedCategory.value === 'all') {
      return viewPresets.value;
    }
    return viewPresets.value.filter((preset) => preset.type === selectedCategory.value);
  });

  // 初始化幻灯片数据
  onMounted(() => {
    initSlides();
    // 加载视角预设数据
    globalThreeStore.loadViewPresetsFromStorage();
    // 加载PPT视角绑定数据
    globalThreeStore.loadPPTViewBindingsFromStorage();
  });

  function initSlides() {
    slides.value = Array.from({ length: totalSlides.value }, (_, index) => ({
      id: index,
      name: `幻灯片 ${index + 1}`,
    }));
  }

  // 获取幻灯片的绑定信息
  function getSlideBinding(slideIndex: number): PPTViewBinding | undefined {
    return globalThreeStore.getPPTViewBinding(slideIndex);
  }

  function selectSlide(index: number) {
    currentSlide.value = index;
  }

  function bindCurrentView() {
    emit('bind-current', currentSlide.value);
    // 模拟获取当前视角数据
    const mockViewBinding: ViewBinding = {
      position: { x: Math.random() * 10, y: Math.random() * 10, z: Math.random() * 10 },
      target: { x: Math.random() * 5, y: Math.random() * 5, z: Math.random() * 5 },
    };
    slides.value[currentSlide.value].viewBinding = mockViewBinding;
  }

  function previewBinding() {
    if (slides.value[currentSlide.value].viewBinding) {
      emit('preview-binding', currentSlide.value);
    }
  }

  function clearBinding(index: number) {
    globalThreeStore.removePPTViewBinding(index);
    message.success(`已清除幻灯片 ${index + 1} 的视角绑定`);
  }

  function handleCancel() {
    emit('update:visible', false);
  }

  function handleConfirm() {
    const bindings = slides.value.map((slide) => slide.viewBinding).filter(Boolean) as ViewBinding[];
    emit('confirm', bindings);
    emit('update:visible', false);
  }

  // 显示预设选择器
  function showPresetSelector(slideIndex: number) {
    selectedSlideForPreset.value = slideIndex;
    showPresetModal.value = true;
  }

  // 关闭预设选择器
  function closePresetModal() {
    showPresetModal.value = false;
    selectedSlideForPreset.value = -1;
  }

  // 选择预设视角
  async function selectPreset(preset: ViewPreset) {
    if (selectedSlideForPreset.value >= 0) {
      try {
        // 应用预设的功能特性
        await globalThreeStore.applyPresetFeatures(preset);

        // 创建增强的视角绑定，包含场景类型和设备信息
        const enhancedBinding: PPTViewBinding = {
          slideIndex: selectedSlideForPreset.value,
          cameraPosition: { ...preset.cameraPosition },
          cameraTarget: { ...preset.cameraTarget },
          name: `${preset.name}`,
          sceneType: preset.features?.sceneType,
          deviceFocus: preset.features?.deviceFocus,
          features: {
            transparency: preset.features?.transparency,
            patrol: preset.features?.patrol,
          },
        };

        // 直接添加到全局存储
        globalThreeStore.addPPTViewBinding(enhancedBinding);

        message.success(
          `已应用预设"${preset.name}"到幻灯片 ${selectedSlideForPreset.value + 1}${preset.features?.sceneType ? ` (${preset.features.sceneType === 'exterior' ? '外景' : '内景'})` : ''}`
        );
        closePresetModal();
      } catch (error) {
        console.error('应用预设失败:', error);
        message.error('应用预设失败，请重试');
      }
    }
  }

  // 预览预设视角
  async function previewPreset(preset: ViewPreset) {
    try {
      const cameraController = CameraController.getInstance();
      if (cameraController) {
        // 设置相机位置和目标
        cameraController.setCameraPosition(preset.cameraPosition.x, preset.cameraPosition.y, preset.cameraPosition.z);
        cameraController.setCameraTarget(preset.cameraTarget.x, preset.cameraTarget.y, preset.cameraTarget.z);

        // 应用预设的功能特性（预览模式）
        await globalThreeStore.applyPresetFeatures(preset);

        message.info(`正在预览"${preset.name}"视角`);
      }
    } catch (error) {
      console.error('预览预设失败:', error);
      message.error('预览预设失败，请重试');
    }
  }

  // 创建新预设
  function createNewPreset() {
    const presetName = prompt('请输入预设名称:');
    if (presetName && presetName.trim()) {
      const description = prompt('请输入预设描述（可选）:');
      const preset = globalThreeStore.createViewPresetFromCurrentCamera(presetName.trim(), description?.trim());
      if (preset) {
        // 可以选择是否立即应用这个新预设
        if (confirm('预设创建成功！是否立即应用到当前幻灯片？')) {
          selectPreset(preset);
        }
      } else {
        alert('创建预设失败，请确保3D场景已正确加载');
      }
    }
  }

  // 强制重新创建默认预设
  function forceRecreatePresets() {
    if (confirm('这将重新创建所有默认预设（包含功能特性），您的自定义预设不会受影响。是否继续？')) {
      globalThreeStore.forceRecreateDefaultPresets();
      message.success('默认预设已重新创建，现在包含完整的功能特性！');
    }
  }

  // 格式化日期
  function formatDate(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  }
</script>
