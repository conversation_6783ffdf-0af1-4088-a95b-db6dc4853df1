import { defineStore } from 'pinia';
import { nextTick, ref, type Ref } from 'vue';
import { ModelLoaderManager } from '../lib/load/ModelLoaderManager';
import { buildingData } from '@/data/buildingData';
import { DebugGUIManager } from '../lib/debug/DebugGUIManager';
import * as THREE from 'three';

// PPT演示相关接口
export interface PPTViewBinding {
  slideIndex: number;
  cameraPosition: { x: number; y: number; z: number };
  cameraTarget: { x: number; y: number; z: number };
  name: string;
  sceneType?: 'interior' | 'exterior'; // 场景类型
  deviceFocus?: string; // 聚焦的设备名称
  features?: {
    transparency?: boolean; // 是否启用透视
    patrol?: boolean; // 是否启用巡检
  };
}

// 视角预设类型
export type PresetType = 'basic' | 'interior' | 'exterior' | 'device' | 'special' | 'custom';

// 视角预设功能
export interface PresetFeatures {
  transparency?: boolean; // 是否启用透视
  patrol?: boolean; // 是否启用巡检
  deviceFocus?: string; // 聚焦的设备名称
  sceneType?: 'interior' | 'exterior'; // 场景类型
}

// 视角预设接口
export interface ViewPreset {
  id: string;
  name: string;
  cameraPosition: { x: number; y: number; z: number };
  cameraTarget: { x: number; y: number; z: number };
  description?: string;
  createdAt: number;
  type: PresetType; // 预设类型
  features?: PresetFeatures; // 预设功能
  icon?: string; // 预设图标
}

export interface PPTDemonstrationState {
  isActive: boolean;
  currentSlide: number;
  totalSlides: number;
  viewBindings: PPTViewBinding[];
  isPlaying: boolean;
}

// Store 状态接口
interface GlobalThreeState {
  containerRef: Ref<HTMLElement | null>; // 修复 Ref 类型问题
  loadingProgress: number;
  currentView: 1 | 2; // 限制为具体的值
  currentFloorId: string | null;
  enableModelLoading: boolean;
  isViewSwitching: boolean;
  appReady: boolean;
  loadingComplete: boolean;
  totalModelsToLoad: number;
  loadedModelsCount: number;
  silentLoadingProgress: number;
  loadingErrors: string[];
  transparencyMode: boolean; // 添加透视模式状态
  isFloorSwitching: boolean; // 新增状态：是否正在切换楼层
  isPatrolActive: boolean; // 新增状态：巡检功能是否激活
  isPlayActive: boolean; // 新增状态：播放功能是否激活
  isRoamingActive: boolean; // 新增状态：漫游功能是否激活
  isDeviceDetailActive: boolean; // 新增状态：设备详情是否激活
  currentDeviceCode: string | null; // 新增状态：当前查看的设备编码
  viewPresets: ViewPreset[]; // 视角预设列表
  interiorDebugSettings: SceneDebugSettings; // 内景调试设置
  pptDemonstration: PPTDemonstrationState; // PPT演示状态
  exteriorDebugSettings: SceneDebugSettings; // 外景调试设置
  currentDebugSettings?: SceneDebugSettings; // 当前调试设置
}

// 场景调试设置接口
interface SceneDebugSettings {
  lightSettings: {
    intensity: number;
    shadow: boolean;
  };
  cameraSettings: {
    alpha: number;
    beta: number;
    distance: number;
  };
  renderSettings: {
    toneMapping: string;
    exposure: number;
  };
}

// 加载错误接口
interface LoadError {
  message: string;
  timestamp: number;
  details?: unknown;
}

export const useGlobalThreeStore = defineStore({
  id: 'globalThree',
  state: (): GlobalThreeState => ({
    containerRef: ref(null) as Ref<HTMLElement | null>, // 修复 Ref 类型问题
    loadingProgress: 0,
    currentView: 1, // 1: 总览, 2: 详情
    currentFloorId: null,
    enableModelLoading: true, // 添加模型加载开关
    isViewSwitching: false, // 添加视图切换状态
    appReady: false, // 新增：表示应用程序是否准备就绪
    loadingComplete: false, // 新增：表示所有加载是否完成
    totalModelsToLoad: 0, // 新增：需要加载的模型总数
    loadedModelsCount: 0, // 新增：已加载完成的模型数量
    silentLoadingProgress: 0, // 新增：静默加载进度
    loadingErrors: [], // 新增：加载过程中的错误
    transparencyMode: false, // 添加透视模式状态
    isFloorSwitching: false, // 新增状态：是否正在切换楼层
    isPatrolActive: false, // 新增状态：巡检功能是否激活
    isPlayActive: false, // 新增状态：播放功能是否激活
    isRoamingActive: false, // 新增状态：漫游功能是否激活
    isDeviceDetailActive: false, // 新增状态：设备详情是否激活
    currentDeviceCode: null, // 新增状态：当前查看的设备编码
    viewPresets: [], // 视角预设列表
    pptDemonstration: {
      isActive: false,
      currentSlide: 0,
      totalSlides: 0,
      viewBindings: [],
      isPlaying: false,
    },
    interiorDebugSettings: {
      lightSettings: {
        intensity: 1.0,
        shadow: true,
      },
      cameraSettings: {
        alpha: 20,
        beta: 40,
        distance: 200,
      },
      renderSettings: {
        toneMapping: 'ACESFilmicToneMapping',
        exposure: 0.9,
      },
    },
    exteriorDebugSettings: {
      lightSettings: {
        intensity: 1.2,
        shadow: true,
      },
      cameraSettings: {
        alpha: 30,
        beta: 50,
        distance: 250,
      },
      renderSettings: {
        toneMapping: 'LinearToneMapping',
        exposure: 1.0,
      },
    },
    currentDebugSettings: undefined,
  }),

  getters: {
    isReady(): boolean {
      return this.loadingComplete && this.appReady;
    },
    loadingPercentage(): number {
      if (this.totalModelsToLoad === 0) return 0;
      return Math.min(100, Math.floor((this.loadedModelsCount / this.totalModelsToLoad) * 100));
    },
    canUserInteract(): boolean {
      return this.loadingComplete && this.appReady;
    },
  },

  actions: {
    setContainerRef(containerRef: Ref<HTMLElement | null>): void {
      this.containerRef = containerRef;
    },

    setLoadModelLoading(loading: number): void {
      this.loadingProgress = loading;
    },

    async toggleView(): Promise<void> {
      // 重置透视模式状态
      this.resetTransparencyMode();

      const modelLoader = ModelLoaderManager.getInstance();
      const previousView = this.currentView;
      this.isViewSwitching = true;

      try {
        if (this.currentView === 1) {
          this.currentView = 2;
          await nextTick();

          const firstFloor = buildingData.floors?.[0];
          if (!firstFloor) {
            throw new Error('找不到楼层数据');
          }

          try {
            await modelLoader.showFloor(firstFloor.id);
            this.currentFloorId = firstFloor.id;
            this.switchSceneDebugSettings('interior');
          } catch (error) {
            console.error('楼层模型加载失败:', error);
            this.currentView = previousView;
            throw error;
          }
        } else {
          this.currentFloorId = null;
          await nextTick();
          this.currentView = 1;

          if (!buildingData.modelPath) {
            throw new Error('缺少外景模型路径');
          }

          try {
            await modelLoader.showBuilding(buildingData.modelPath);
            this.switchSceneDebugSettings('exterior');
          } catch (error) {
            console.error('建筑模型加载失败:', error);
            this.currentView = previousView;
            throw error;
          }
        }
      } catch (error) {
        console.error('视图切换失败:', error);
        this.currentView = previousView;
        if (window.$message) {
          window.$message.error('视图切换失败，请重试');
        }
        throw error;
      } finally {
        this.isViewSwitching = false;
      }
    },

    setLoadingProgress(progress: number): void {
      this.loadingProgress = progress;
    },

    setCurrentFloorId(floorId: string | null): void {
      this.currentFloorId = floorId;
    },

    setEnableModelLoading(enable: boolean): void {
      this.enableModelLoading = enable;
    },

    setAppReady(value: boolean): void {
      this.appReady = value;
      console.log(`[GlobalThreeStore] 应用程序就绪状态已设置为: ${value}`);

      if (value && !this.loadingComplete) {
        console.log('[GlobalThreeStore] 强制同步加载完成状态');
        this.loadingComplete = true;
      }
    },

    setLoadingComplete(value: boolean): void {
      this.loadingComplete = value;
      console.log(`[GlobalThreeStore] 加载完成状态已设置为: ${value}, 已加载模型数: ${this.loadedModelsCount}/${this.totalModelsToLoad}`);

      if (value && this.loadingProgress >= 100 && !this.appReady) {
        console.log('[GlobalThreeStore] 强制同步应用程序就绪状态');
        this.appReady = true;
      }
    },

    forceCompleteLoading(): void {
      console.log('[GlobalThreeStore] 用户请求强制完成加载流程');
      console.log(`[GlobalThreeStore] 当前状态 - 加载完成: ${this.loadingComplete}, 应用就绪: ${this.appReady}`);
      console.log(`[GlobalThreeStore] 当前进度 - 主模型: ${this.loadingProgress}%, 静默加载: ${this.silentLoadingProgress}%`);
      console.log(`[GlobalThreeStore] 已加载模型数: ${this.loadedModelsCount}/${this.totalModelsToLoad}`);

      const loadedPercentage = this.totalModelsToLoad > 0 ? (this.loadedModelsCount / this.totalModelsToLoad) * 100 : 0;

      if (loadedPercentage < 80) {
        console.warn(`[GlobalThreeStore] 已加载模型比例过低 (${loadedPercentage.toFixed(2)}%)，可能导致场景显示不完整`);
        if (window.$message) {
          window.$message.warning('模型加载未完成，强制进入可能导致场景显示不完整');
        }
      }

      this.loadingComplete = true;
      this.appReady = true;
      this.loadingProgress = 100;
      this.silentLoadingProgress = 100;

      const actualLoaded = this.loadedModelsCount;
      this.loadedModelsCount = this.totalModelsToLoad;

      console.log(`[GlobalThreeStore] 已强制完成加载流程，实际已加载: ${actualLoaded}/${this.totalModelsToLoad} 模型`);
    },

    setModelsLoadInfo(total: number, loaded: number): void {
      this.totalModelsToLoad = total;
      this.loadedModelsCount = loaded;
    },

    incrementLoadedModels(): void {
      this.loadedModelsCount++;
    },

    setSilentLoadingProgress(value: number): void {
      this.silentLoadingProgress = value;
    },

    addLoadingError(error: LoadError): void {
      this.loadingErrors.push(error.message);
    },

    resetLoadingState(): void {
      this.loadingProgress = 0;
      this.loadingComplete = false;
      this.totalModelsToLoad = 0;
      this.loadedModelsCount = 0;
      this.silentLoadingProgress = 0;
      this.loadingErrors = [];
    },

    $reset(): void {
      this.containerRef = ref(null) as Ref<HTMLElement | null>; // 修复 Ref 类型问题
      this.enableModelLoading = false;
      this.loadingProgress = 0;
      this.currentView = 1;
      this.currentFloorId = null;
      this.appReady = false;
      this.loadingComplete = false;
      this.totalModelsToLoad = 0;
      this.loadedModelsCount = 0;
      this.silentLoadingProgress = 0;
      this.loadingErrors = [];
      this.transparencyMode = false;
      this.isFloorSwitching = false;
      this.isPatrolActive = false;
      this.isPlayActive = false;
      this.isRoamingActive = false;
      this.isDeviceDetailActive = false;
      this.currentDeviceCode = null;
      this.viewPresets = [];
      this.pptDemonstration = {
        isActive: false,
        currentSlide: 0,
        totalSlides: 0,
        viewBindings: [],
        isPlaying: false,
      };
      this.currentDebugSettings = undefined;
    },

    // 设置透视模式状态
    setTransparencyMode(value: boolean): void {
      // 如果要启用透视模式，但巡检功能处于活跃状态，则先关闭巡检功能
      if (value && this.isPatrolActive) {
        console.log('[GlobalThreeStore] 启用透视模式前自动关闭巡检功能');
        // 触发自定义事件通知巡检组件停止
        window.dispatchEvent(
          new CustomEvent('transparency-activated', {
            detail: { message: '已自动关闭巡检功能以启用透视模式' },
          })
        );
        this.isPatrolActive = false;
      }

      this.transparencyMode = value;
    },

    // 重置透视模式
    resetTransparencyMode(): void {
      this.transparencyMode = false;
    },

    // 设置楼层切换状态
    setFloorSwitching(status: boolean): void {
      this.isFloorSwitching = status;
    },

    // 设置巡检激活状态
    setPatrolActive(status: boolean): void {
      this.isPatrolActive = status;

      // 如果激活巡检，确保播放功能被禁用
      if (status && this.isPlayActive) {
        console.log('[GlobalThreeStore] 巡检功能激活，自动停止播放功能');
        this.isPlayActive = false;
        // 触发自定义事件通知播放组件停止
        window.dispatchEvent(new CustomEvent('patrol-activated'));
      }
    },

    // 设置播放激活状态
    setPlayActive(status: boolean): void {
      this.isPlayActive = status;

      // 如果激活播放，确保巡检功能被禁用
      if (status && this.isPatrolActive) {
        console.log('[GlobalThreeStore] 播放功能激活，自动停止巡检功能');
        this.isPatrolActive = false;
        // 触发自定义事件通知巡检组件停止
        window.dispatchEvent(new CustomEvent('play-activated'));
      }

      // 如果激活播放，确保漫游功能被禁用
      if (status && this.isRoamingActive) {
        console.log('[GlobalThreeStore] 播放功能激活，自动停止漫游功能');
        this.isRoamingActive = false;
        // 触发自定义事件通知漫游组件停止
        window.dispatchEvent(new CustomEvent('play-activated'));
      }
    },

    // 设置漫游激活状态
    setRoamingActive(status: boolean): void {
      this.isRoamingActive = status;

      // 如果激活漫游，确保播放功能被禁用
      if (status && this.isPlayActive) {
        console.log('[GlobalThreeStore] 漫游功能激活，自动停止播放功能');
        this.isPlayActive = false;
        // 触发自定义事件通知播放组件停止
        window.dispatchEvent(new CustomEvent('roaming-activated'));
      }

      // 如果激活漫游，确保巡检功能被禁用
      if (status && this.isPatrolActive) {
        console.log('[GlobalThreeStore] 漫游功能激活，自动停止巡检功能');
        this.isPatrolActive = false;
        // 触发自定义事件通知巡检组件停止
        window.dispatchEvent(new CustomEvent('roaming-activated'));
      }
    },

    // 设置设备详情激活状态
    setDeviceDetailActive(status: boolean, deviceCode?: string | null): void {
      if (!status) {
        this.currentDeviceCode = null;
      } else if (deviceCode) {
        this.currentDeviceCode = deviceCode;
      }
      this.isDeviceDetailActive = status;
    },

    // 初始化调试设置
    initializeDebugSettings(sceneType: 'interior' | 'exterior'): void {
      if (sceneType === 'interior') {
        this.currentDebugSettings = { ...this.interiorDebugSettings };
      } else {
        this.currentDebugSettings = { ...this.exteriorDebugSettings };
      }
    },

    // 保存当前调试设置
    saveCurrentDebugSettings(sceneType: 'interior' | 'exterior'): void {
      const defaultSettings: SceneDebugSettings = {
        lightSettings: { intensity: 1.0, shadow: true },
        cameraSettings: { alpha: 0, beta: 0, distance: 0 },
        renderSettings: { toneMapping: 'LinearToneMapping', exposure: 1.0 },
      };

      const completeSettings = {
        lightSettings: { ...defaultSettings.lightSettings, ...this.currentDebugSettings?.lightSettings },
        cameraSettings: { ...defaultSettings.cameraSettings, ...this.currentDebugSettings?.cameraSettings },
        renderSettings: { ...defaultSettings.renderSettings, ...this.currentDebugSettings?.renderSettings },
      };

      if (sceneType === 'interior') {
        this.interiorDebugSettings = completeSettings;
      } else {
        this.exteriorDebugSettings = completeSettings;
      }
    },

    // 切换场景调试设置
    switchSceneDebugSettings(sceneType: 'interior' | 'exterior'): void {
      if (sceneType === 'interior') {
        this.currentDebugSettings = { ...this.interiorDebugSettings };
      } else {
        this.currentDebugSettings = { ...this.exteriorDebugSettings };
      }

      // 通知调试器更新场景类型
      const debugManager = DebugGUIManager.getInstance();
      if (debugManager) {
        debugManager.setSceneType(sceneType);
      }
    },

    // PPT演示相关方法
    setPPTDemonstrationActive(status: boolean): void {
      this.pptDemonstration.isActive = status;

      // 如果激活PPT演示，禁用其他功能
      if (status) {
        if (this.isPatrolActive) {
          this.isPatrolActive = false;
          window.dispatchEvent(new CustomEvent('ppt-demonstration-activated'));
        }
        if (this.isPlayActive) {
          this.isPlayActive = false;
          window.dispatchEvent(new CustomEvent('ppt-demonstration-activated'));
        }
        if (this.isRoamingActive) {
          this.isRoamingActive = false;
          window.dispatchEvent(new CustomEvent('ppt-demonstration-activated'));
        }
        if (this.transparencyMode) {
          this.transparencyMode = false;
        }
      }
    },

    setPPTCurrentSlide(slideIndex: number): void {
      this.pptDemonstration.currentSlide = slideIndex;
    },

    setPPTTotalSlides(total: number): void {
      this.pptDemonstration.totalSlides = total;
    },

    setPPTPlaying(isPlaying: boolean): void {
      this.pptDemonstration.isPlaying = isPlaying;
    },

    // PPT视角绑定localStorage存储键名
    PPT_VIEW_BINDINGS_STORAGE_KEY: 'ppt-view-bindings',

    // 从localStorage加载视角绑定数据
    loadPPTViewBindingsFromStorage(): void {
      try {
        const stored = localStorage.getItem(this.PPT_VIEW_BINDINGS_STORAGE_KEY);
        if (stored) {
          const bindings = JSON.parse(stored);
          this.pptDemonstration.viewBindings = bindings;
          console.log(`已从localStorage加载 ${bindings.length} 个视角绑定`);
        }
      } catch (error) {
        console.error('加载视角绑定数据失败:', error);
        this.pptDemonstration.viewBindings = [];
      }
    },

    // 保存视角绑定数据到localStorage
    savePPTViewBindingsToStorage(): void {
      try {
        const dataToSave = JSON.stringify(this.pptDemonstration.viewBindings);
        localStorage.setItem(this.PPT_VIEW_BINDINGS_STORAGE_KEY, dataToSave);
        console.log(`已保存 ${this.pptDemonstration.viewBindings.length} 个视角绑定到localStorage`);
      } catch (error) {
        console.error('保存视角绑定数据失败:', error);
      }
    },

    addPPTViewBinding(binding: PPTViewBinding): void {
      const existingIndex = this.pptDemonstration.viewBindings.findIndex((b) => b.slideIndex === binding.slideIndex);
      if (existingIndex >= 0) {
        this.pptDemonstration.viewBindings[existingIndex] = binding;
      } else {
        this.pptDemonstration.viewBindings.push(binding);
      }
      // 自动保存到localStorage
      this.savePPTViewBindingsToStorage();
    },

    removePPTViewBinding(slideIndex: number): void {
      this.pptDemonstration.viewBindings = this.pptDemonstration.viewBindings.filter((b) => b.slideIndex !== slideIndex);
      // 自动保存到localStorage
      this.savePPTViewBindingsToStorage();
    },

    getPPTViewBinding(slideIndex: number): PPTViewBinding | undefined {
      return this.pptDemonstration.viewBindings.find((b) => b.slideIndex === slideIndex);
    },

    clearPPTViewBindings(): void {
      this.pptDemonstration.viewBindings = [];
      // 清空localStorage
      try {
        localStorage.removeItem(this.PPT_VIEW_BINDINGS_STORAGE_KEY);
        console.log('已清空localStorage中的视角绑定数据');
      } catch (error) {
        console.error('清空localStorage中的视角绑定数据失败:', error);
      }
    },

    // 视角预设管理localStorage存储键名
    VIEW_PRESETS_STORAGE_KEY: 'view-presets',

    // 从localStorage加载视角预设数据
    loadViewPresetsFromStorage(): void {
      try {
        const stored = localStorage.getItem(this.VIEW_PRESETS_STORAGE_KEY);
        if (stored) {
          const presets = JSON.parse(stored);

          // 检查是否需要更新预设（如果预设没有features字段，说明是旧版本）
          const needsUpdate = presets.some(
            (preset: ViewPreset) =>
              (preset.type === 'exterior' || preset.type === 'interior' || preset.type === 'device' || preset.type === 'special') && !preset.features
          );

          if (needsUpdate) {
            console.log('检测到旧版本预设，重新创建默认预设');
            this.createDefaultPresets();
          } else {
            this.viewPresets = presets;
            console.log(`已从localStorage加载 ${presets.length} 个视角预设`);
          }
        } else {
          // 如果没有存储的预设，创建一些默认预设
          this.createDefaultPresets();
        }
      } catch (error) {
        console.error('加载视角预设数据失败:', error);
        this.viewPresets = [];
        // 创建默认预设
        this.createDefaultPresets();
      }
    },

    // 强制重新创建默认预设（用于调试和更新）
    forceRecreateDefaultPresets(): void {
      console.log('强制重新创建默认预设');
      this.createDefaultPresets();
    },

    // 扫描场景中的设备并创建聚焦预设
    async createDeviceFocusPresets(): Promise<void> {
      try {
        console.log('[GlobalThreeStore] 开始扫描场景中的设备...');

        // 动态导入相关模块
        const { ModelLoaderManager } = await import('../lib/load/ModelLoaderManager');
        const modelLoader = ModelLoaderManager.getInstance();
        const models = modelLoader.getCurrentModels();

        if (!models || models.length === 0) {
          console.warn('[GlobalThreeStore] 没有找到加载的模型');
          return;
        }

        const devices: Array<{ name: string; object: THREE.Object3D; position: THREE.Vector3 }> = [];
        const processedNames = new Set<string>();

        // 遍历所有模型查找设备
        models.forEach((model) => {
          model.traverse((object) => {
            // 使用项目中的设备识别逻辑：以1F、2F、3F、4F开头的设备
            if (object.name && /^[1-4]F/.test(object.name) && !processedNames.has(object.name)) {
              processedNames.add(object.name);

              // 计算设备的世界坐标
              const worldPosition = new THREE.Vector3();
              object.getWorldPosition(worldPosition);

              devices.push({
                name: object.name,
                object: object,
                position: worldPosition,
              });
            }
          });
        });

        console.log(`[GlobalThreeStore] 找到 ${devices.length} 个设备`);

        // 为每个设备创建聚焦预设
        devices.forEach((device, index) => {
          // 解析设备名称获取楼层和类型信息
          const floorMatch = device.name.match(/(\d+)F/);
          const floor = floorMatch ? floorMatch[1] : '未知';

          // 解析设备类型
          let deviceType = '设备';
          let icon = '🖥️';

          const name = device.name.toLowerCase();
          if (name.includes('jg') || name.includes('机柜')) {
            deviceType = '机柜';
            icon = '🗄️';
          } else if (name.includes('fwq') || name.includes('服务器')) {
            deviceType = '服务器';
            icon = '🖥️';
          } else if (name.includes('pdg') || name.includes('配电')) {
            deviceType = '配电柜';
            icon = '⚡';
          } else if (name.includes('kzg') || name.includes('控制')) {
            deviceType = '控制柜';
            icon = '🎛️';
          }

          // 计算相机位置（在设备前方适当距离）
          const cameraPosition = {
            x: device.position.x - 8, // 在设备前方8米
            y: device.position.y + 5, // 高度提升5米
            z: device.position.z - 8, // 稍微偏移
          };

          // 创建设备聚焦预设
          const devicePreset: ViewPreset = {
            id: `device_${device.name.replace(/[^a-zA-Z0-9]/g, '_')}`,
            name: `${floor}楼${deviceType}`,
            description: `聚焦到${device.name}设备`,
            cameraPosition: cameraPosition,
            cameraTarget: {
              x: device.position.x,
              y: device.position.y + 2, // 目标点稍微高一点
              z: device.position.z,
            },
            type: 'device',
            icon: icon,
            features: {
              deviceFocus: device.name,
              sceneType: 'interior', // 设备聚焦通常在内景
            },
            createdAt: Date.now(),
          };

          // 检查是否已存在相同的预设
          const existingPreset = this.viewPresets.find((p) => p.id === devicePreset.id);
          if (!existingPreset) {
            this.viewPresets.push(devicePreset);
          }
        });

        // 保存更新后的预设
        this.saveViewPresetsToStorage();
        console.log(`[GlobalThreeStore] 已创建 ${devices.length} 个设备聚焦预设`);
      } catch (error) {
        console.error('[GlobalThreeStore] 创建设备聚焦预设失败:', error);
      }
    },

    // 创建默认预设
    createDefaultPresets(): void {
      const defaultPresets: ViewPreset[] = [
        // 基础视角预设
        {
          id: 'basic_overview',
          name: '总览视角',
          description: '建筑物整体俯视角度',
          cameraPosition: { x: -45.45, y: 45.2, z: -70.2 },
          cameraTarget: { x: 0, y: 10, z: 0 },
          type: 'basic',
          icon: '🏢',
          createdAt: Date.now(),
        },
        {
          id: 'basic_front',
          name: '正面视角',
          description: '建筑物正面视角',
          cameraPosition: { x: 0, y: 20, z: -100 },
          cameraTarget: { x: 0, y: 10, z: 0 },
          type: 'basic',
          icon: '📐',
          createdAt: Date.now(),
        },
        {
          id: 'basic_side',
          name: '侧面视角',
          description: '建筑物侧面视角',
          cameraPosition: { x: -100, y: 20, z: 0 },
          cameraTarget: { x: 0, y: 10, z: 0 },
          type: 'basic',
          icon: '📏',
          createdAt: Date.now(),
        },
        {
          id: 'basic_top',
          name: '顶部视角',
          description: '建筑物顶部俯视视角',
          cameraPosition: { x: 0, y: 100, z: 0 },
          cameraTarget: { x: 0, y: 0, z: 0 },
          type: 'basic',
          icon: '🔝',
          createdAt: Date.now(),
        },

        // 外景预设
        {
          id: 'exterior_wide',
          name: '外景广角',
          description: '建筑物外部广角视角',
          cameraPosition: { x: -80, y: 60, z: -120 },
          cameraTarget: { x: 0, y: 20, z: 0 },
          type: 'exterior',
          icon: '🌅',
          features: { sceneType: 'exterior' },
          createdAt: Date.now(),
        },
        {
          id: 'exterior_entrance',
          name: '外景入口',
          description: '建筑物入口视角',
          cameraPosition: { x: -20, y: 15, z: -60 },
          cameraTarget: { x: 0, y: 10, z: 0 },
          type: 'exterior',
          icon: '🚪',
          features: { sceneType: 'exterior' },
          createdAt: Date.now(),
        },

        // 内景预设
        {
          id: 'interior_main',
          name: '内景主厅',
          description: '建筑物内部主厅视角',
          cameraPosition: { x: -10, y: 8, z: -15 },
          cameraTarget: { x: 0, y: 5, z: 0 },
          type: 'interior',
          icon: '🏛️',
          features: { sceneType: 'interior' },
          createdAt: Date.now(),
        },
        {
          id: 'interior_corridor',
          name: '内景走廊',
          description: '建筑物内部走廊视角',
          cameraPosition: { x: -5, y: 6, z: -20 },
          cameraTarget: { x: 0, y: 3, z: 10 },
          type: 'interior',
          icon: '🚶',
          features: { sceneType: 'interior' },
          createdAt: Date.now(),
        },

        // 设备聚焦预设
        {
          id: 'device_server_room',
          name: '服务器机房',
          description: '聚焦服务器设备区域',
          cameraPosition: { x: -15, y: 12, z: -25 },
          cameraTarget: { x: 0, y: 8, z: 0 },
          type: 'device',
          icon: '🖥️',
          features: { deviceFocus: 'server' },
          createdAt: Date.now(),
        },
        {
          id: 'device_network',
          name: '网络设备',
          description: '聚焦网络设备区域',
          cameraPosition: { x: -8, y: 10, z: -18 },
          cameraTarget: { x: 2, y: 6, z: 5 },
          type: 'device',
          icon: '🌐',
          features: { deviceFocus: 'network' },
          createdAt: Date.now(),
        },

        // 特殊功能预设
        {
          id: 'special_transparency',
          name: '透视模式',
          description: '启用透视功能的总览视角',
          cameraPosition: { x: -35, y: 35, z: -55 },
          cameraTarget: { x: 0, y: 15, z: 0 },
          type: 'special',
          icon: '👁️',
          features: { transparency: true },
          createdAt: Date.now(),
        },
        {
          id: 'special_patrol_start',
          name: '巡检起点',
          description: '适合开始巡检的视角位置',
          cameraPosition: { x: -25, y: 25, z: -40 },
          cameraTarget: { x: 0, y: 10, z: 0 },
          type: 'special',
          icon: '🚶‍♂️',
          features: { patrol: true },
          createdAt: Date.now(),
        },
      ];

      this.viewPresets = defaultPresets;
      this.saveViewPresetsToStorage();
      console.log('已创建默认视角预设');
    },

    // 保存视角预设数据到localStorage
    saveViewPresetsToStorage(): void {
      try {
        const dataToSave = JSON.stringify(this.viewPresets);
        localStorage.setItem(this.VIEW_PRESETS_STORAGE_KEY, dataToSave);
        console.log(`已保存 ${this.viewPresets.length} 个视角预设到localStorage`);
      } catch (error) {
        console.error('保存视角预设数据失败:', error);
      }
    },

    // 添加视角预设
    addViewPreset(preset: ViewPreset): void {
      const existingIndex = this.viewPresets.findIndex((p) => p.id === preset.id);
      if (existingIndex >= 0) {
        this.viewPresets[existingIndex] = preset;
      } else {
        this.viewPresets.push(preset);
      }
      // 自动保存到localStorage
      this.saveViewPresetsToStorage();
    },

    // 删除视角预设
    removeViewPreset(presetId: string): void {
      this.viewPresets = this.viewPresets.filter((p) => p.id !== presetId);
      // 自动保存到localStorage
      this.saveViewPresetsToStorage();
    },

    // 获取视角预设
    getViewPreset(presetId: string): ViewPreset | undefined {
      return this.viewPresets.find((p) => p.id === presetId);
    },

    // 清空所有视角预设
    clearViewPresets(): void {
      this.viewPresets = [];
      // 清空localStorage
      try {
        localStorage.removeItem(this.VIEW_PRESETS_STORAGE_KEY);
        console.log('已清空localStorage中的视角预设数据');
      } catch (error) {
        console.error('清空localStorage中的视角预设数据失败:', error);
      }
    },

    // 从当前相机位置创建视角预设
    createViewPresetFromCurrentCamera(name: string, description?: string): ViewPreset | null {
      try {
        // 这里需要从CameraController获取当前相机位置
        // 由于CameraController是单例，我们可以直接获取
        const { CameraController } = require('../lib/CameraController');
        const cameraController = CameraController.getInstance();

        if (cameraController) {
          const position = cameraController.getCameraPosition();
          const target = cameraController.currentTarget || { x: 0, y: 0, z: 0 };

          const preset: ViewPreset = {
            id: `preset_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            name,
            description,
            cameraPosition: { x: position.x, y: position.y, z: position.z },
            cameraTarget: { x: target.x, y: target.y, z: target.z },
            type: 'custom',
            createdAt: Date.now(),
          };

          this.addViewPreset(preset);
          return preset;
        }
      } catch (error) {
        console.error('创建视角预设失败:', error);
      }
      return null;
    },

    // 应用预设的功能特性
    async applyPresetFeatures(preset: ViewPreset): Promise<void> {
      if (!preset.features) return;

      try {
        console.log(`[GlobalThreeStore] 应用预设功能特性:`, preset.features);

        // 应用场景类型
        if (preset.features.sceneType) {
          const { SceneManager } = require('../lib/SceneManager');
          const { ModelLoaderManager } = require('../lib/load/ModelLoaderManager');

          const sceneManager = SceneManager.getInstance();
          const modelLoader = ModelLoaderManager.getInstance();

          if (preset.features.sceneType === 'exterior') {
            sceneManager?.switchSceneType('exterior');
            await modelLoader?.showExterior();
          } else if (preset.features.sceneType === 'interior') {
            sceneManager?.switchSceneType('interior');
            await modelLoader?.showInterior();
          }
        }

        // 应用透视功能
        if (preset.features.transparency) {
          const { HighPerformanceTransparencyManager } = require('../lib/HighPerformanceTransparencyManager');
          const transparencyManager = HighPerformanceTransparencyManager.getInstance();
          await transparencyManager.toggleTransparency(true);
          this.setTransparencyMode(true);
        }

        // 应用设备聚焦
        if (preset.features.deviceFocus) {
          // 这里可以添加设备聚焦逻辑
          console.log(`[GlobalThreeStore] 聚焦设备: ${preset.features.deviceFocus}`);
          // 可以调用设备聚焦相关的方法
        }

        // 应用巡检功能
        if (preset.features.patrol) {
          // 这里可以添加巡检准备逻辑
          console.log(`[GlobalThreeStore] 准备巡检模式`);
          // 可以设置巡检相关的状态
        }
      } catch (error) {
        console.error('[GlobalThreeStore] 应用预设功能特性失败:', error);
      }
    },

    // 获取按类型分组的预设
    getPresetsByType(): Record<PresetType, ViewPreset[]> {
      const grouped: Record<PresetType, ViewPreset[]> = {
        basic: [],
        interior: [],
        exterior: [],
        device: [],
        special: [],
        custom: [],
      };

      this.viewPresets.forEach((preset) => {
        grouped[preset.type].push(preset);
      });

      return grouped;
    },
  },
});

// 添加全局类型声明
declare global {
  interface Window {
    $message?: {
      error: (msg: string) => void;
      warning: (msg: string) => void;
    };
  }
}
